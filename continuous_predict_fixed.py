#!/usr/bin/env python3
"""
BTC价格预测 - 修复版持续运行脚本
修复了以下关键问题：
1. 特征工程一致性：统一使用ta库计算技术指标
2. 验证阈值一致性：使用与训练时相同的动态阈值计算方法

功能：
1. 每30分钟自动进行一次BTC价格预测
2. 自动验证上一次预测的准确性（使用训练一致的阈值）
3. 实时统计和显示预测准确率
4. 详细的运行日志和错误处理
5. 支持优雅停止和重启

使用方法：
python continuous_predict_fixed.py

停止方法：
Ctrl+C 或发送 SIGTERM 信号
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import joblib
import os
import warnings
import requests
import ta
import time
import signal
import sys
from datetime import datetime, timedelta
import json

warnings.filterwarnings('ignore', category=UserWarning)

# --- 配置参数 ---
MODEL_DIR = "btc_reflection_model_v1"
PREDICTION_LOG_PATH = os.path.join(MODEL_DIR, "prediction_log.csv")
BINANCE_API_URL = "https://api.binance.com/api/v3/klines"

# 交易配置
SYMBOL = 'BTCUSDT'
INTERVAL = '30m'
KLINES_LIMIT = 150  # 获取足够的历史数据用于特征计算
PREDICTION_INTERVAL = 30 * 60  # 30分钟 = 1800秒

# 全局变量
running = True
model = None
scaler = None
feature_columns = None

def signal_handler(signum, frame):
    """信号处理器，用于优雅停止"""
    global running
    print(f"\n\n🛑 接收到停止信号 ({signum})")
    running = False

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def print_banner():
    """打印启动横幅"""
    print("=" * 80)
    print("🚀 BTC价格预测系统 - 修复版")
    print("=" * 80)
    print("修复内容:")
    print("  ✅ 特征工程一致性（统一使用ta库）")
    print("  ✅ 验证阈值一致性（训练时相同的动态阈值）")
    print("  ✅ 每30分钟自动预测")
    print("  ✅ 自动验证预测准确性")
    print("  ✅ 实时准确率统计")
    print("=" * 80)
    print(f"⏰ 预测间隔: 30 分钟")
    print(f"📊 交易对: {SYMBOL}")
    print(f"⏱️ K线间隔: {INTERVAL}")
    print("按 Ctrl+C 停止运行")
    print("=" * 80)

def load_model_and_preprocessors():
    """加载训练好的模型和预处理器"""
    global model, scaler, feature_columns
    
    try:
        model = joblib.load(os.path.join(MODEL_DIR, "lgbm_model.joblib"))
        scaler = joblib.load(os.path.join(MODEL_DIR, "scaler.joblib"))
        feature_columns = joblib.load(os.path.join(MODEL_DIR, "feature_columns.joblib"))
        
        print("✅ 成功加载模型和预处理器")
        print(f"   特征数量: {len(feature_columns)}")
        print(f"   模型类型: {type(model).__name__}")
        return True
        
    except FileNotFoundError as e:
        print(f"❌ 找不到模型文件 - {e}")
        return False
    except Exception as e:
        print(f"❌ 加载模型失败: {e}")
        return False

def fetch_latest_klines_from_binance(symbol=SYMBOL, interval=INTERVAL, limit=KLINES_LIMIT):
    """从币安API获取最新的K线数据"""
    try:
        limit = min(limit, 1000)
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }

        response = requests.get(BINANCE_API_URL, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        if not data:
            print("API返回空数据")
            return None

        # 转换为DataFrame
        df = pd.DataFrame(data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])

        # 数据类型转换
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume', 'number_of_trades']:
            df[col] = df[col].astype(float)

        # 重命名和选择需要的列
        df = df[['timestamp', 'open', 'high', 'low', 'close', 'volume', 'number_of_trades']].copy()
        df.rename(columns={'timestamp': 'timestamp_utc'}, inplace=True)
        df.set_index('timestamp_utc', inplace=True)

        return df

    except Exception as e:
        print(f"获取币安API数据失败: {e}")
        return None

def create_features(df):
    """
    从原始K线数据创建特征（与训练脚本完全一致，统一使用ta库）
    """
    df_feat = df.copy()

    try:
        # 使用 ta 库计算技术指标，确保与训练脚本完全一致
        # 动量指标
        df_feat['momentum_rsi'] = ta.momentum.RSIIndicator(df_feat['close']).rsi()

        # 趋势指标
        macd = ta.trend.MACD(df_feat['close'])
        df_feat['trend_macd'] = macd.macd()
        df_feat['trend_macd_signal'] = macd.macd_signal()
        df_feat['trend_macd_diff'] = macd.macd_diff()

        # 波动率指标 (布林带)
        bb = ta.volatility.BollingerBands(df_feat['close'])
        df_feat['volatility_bbm'] = bb.bollinger_mavg()
        df_feat['volatility_bbh'] = bb.bollinger_hband()
        df_feat['volatility_bbl'] = bb.bollinger_lband()
        df_feat['volatility_bbw'] = bb.bollinger_wband()
        df_feat['volatility_bbp'] = bb.bollinger_pband()

        # ADX 趋势强度指标
        adx = ta.trend.ADXIndicator(df_feat['high'], df_feat['low'], df_feat['close'])
        df_feat['trend_adx'] = adx.adx()
        df_feat['trend_adx_pos'] = adx.adx_pos()
        df_feat['trend_adx_neg'] = adx.adx_neg()

        # 成交量指标
        df_feat['volume_obv'] = ta.volume.OnBalanceVolumeIndicator(df_feat['close'], df_feat['volume']).on_balance_volume()

        # 基础价格特征
        df_feat['price_change'] = df_feat['close'].pct_change()
        df_feat['high_low_ratio'] = df_feat['high'] / df_feat['low']
        df_feat['body_ratio'] = abs(df_feat['close'] - df_feat['open']) / (df_feat['high'] - df_feat['low'] + 1e-9)

        # 多时间框架特征
        windows = [3, 5, 10, 20, 50, 100]
        for window in windows:
            df_feat[f'ma_{window}'] = df_feat['close'].rolling(window=window).mean()
            df_feat[f'price_ma_{window}_ratio'] = df_feat['close'] / (df_feat[f'ma_{window}'] + 1e-9)
            df_feat[f'momentum_{window}'] = df_feat['close'] / (df_feat['close'].shift(window) + 1e-9) - 1
            df_feat[f'volatility_{window}'] = df_feat['close'].rolling(window=window).std()

        # 额外的技术指标（保持与训练脚本一致）
        delta = df_feat['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-9)
        df_feat['rsi_14'] = 100 - (100 / (1 + rs))

        ema_fast = df_feat['close'].ewm(span=12).mean()
        ema_slow = df_feat['close'].ewm(span=26).mean()
        df_feat['macd'] = ema_fast - ema_slow
        df_feat['macd_signal'] = df_feat['macd'].ewm(span=9).mean()
        df_feat['macd_hist'] = df_feat['macd'] - df_feat['macd_signal']

        df_feat['volume_ma_20'] = df_feat['volume'].rolling(window=20).mean()
        df_feat['volume_ratio_20'] = df_feat['volume'] / (df_feat['volume_ma_20'] + 1e-9)
        df_feat['volume_spike'] = (df_feat['volume'] > df_feat['volume_ma_20'] * 2).astype(int)

        # 清理数据
        df_feat = df_feat.replace([np.inf, -np.inf], np.nan)
        df_feat.dropna(inplace=True)

        return df_feat

    except Exception as e:
        print(f"特征工程过程出错: {e}")
        return pd.DataFrame()

def make_prediction(klines_data):
    """对最新数据进行预测"""
    try:
        # 1. 特征工程
        df_featured = create_features(klines_data)
        
        if df_featured.empty:
            print("⚠️ 特征工程后没有可用数据")
            return None

        # 2. 获取最新的一条数据进行预测
        latest_data = df_featured.iloc[-1:].copy()
        
        # 确保所有需要的特征列都存在
        missing_features = set(feature_columns) - set(latest_data.columns)
        if missing_features:
            print(f"⚠️ 缺少特征列: {missing_features}")
            return None

        # 3. 准备预测数据
        X_latest = latest_data[feature_columns]
        X_latest_scaled = scaler.transform(X_latest)
        
        # 4. 进行预测
        prediction = model.predict(X_latest_scaled)[0]
        prediction_proba = model.predict_proba(X_latest_scaled)[0]
        
        # 5. 准备预测结果
        result = {
            'timestamp': latest_data.index[0],
            'close_price': latest_data['close'].iloc[0],
            'prediction': int(prediction),
            'prediction_proba_down': float(prediction_proba[0]),
            'prediction_proba_up': float(prediction_proba[1]),
            'prediction_confidence': float(max(prediction_proba))
        }
        
        return result
        
    except Exception as e:
        print(f"❌ 预测过程出错: {e}")
        return None

def calculate_training_consistent_threshold(historical_data):
    """
    计算与训练时一致的动态阈值
    """
    try:
        df = historical_data.copy()
        
        # 计算未来回报率（与训练时相同）
        df['future_return'] = df['close'].pct_change(periods=-1).shift(-1)
        
        # 使用与训练时相同的逻辑计算动态阈值
        available_data = len(df)
        if available_data >= 2000:
            window = 2000
            min_periods = 500
        elif available_data >= 500:
            window = available_data
            min_periods = min(500, available_data // 4)
        else:
            window = available_data
            min_periods = max(10, available_data // 10)
        
        # 计算滚动标准差的均值，确保返回单个数值
        rolling_std = df['future_return'].rolling(window=window, min_periods=min_periods).std()
        threshold = float(rolling_std.mean() * 0.5)

        if pd.isna(threshold) or threshold == 0:
            threshold = 0.005  # 0.5%的固定阈值
            
        return threshold, window, min_periods
        
    except Exception as e:
        print(f"计算动态阈值失败: {e}")
        return 0.005, 0, 0

def verify_previous_prediction(current_price, current_timestamp):
    """验证上一次预测的准确性（使用与训练时一致的阈值计算方法）"""
    try:
        if not os.path.exists(PREDICTION_LOG_PATH):
            return None

        # 读取预测日志
        df = pd.read_csv(PREDICTION_LOG_PATH)
        if len(df) == 0:
            return None

        # 修复：明确指定datetime格式，避免解析错误
        df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'], format='%Y-%m-%d %H:%M:%S', errors='coerce')
        # 删除无法解析的时间戳行
        df = df.dropna(subset=['timestamp_utc'])
        if len(df) == 0:
            print("❌ 预测日志中没有有效的时间戳")
            return None

        df.set_index('timestamp_utc', inplace=True)
        df.sort_index(inplace=True)

        # 找到需要验证的预测（30分钟前的预测）
        target_time = current_timestamp - timedelta(minutes=30)

        # 查找最接近目标时间的预测
        time_diffs = abs(df.index - target_time)
        if len(time_diffs) == 0:
            return None

        # 找到最小时间差的索引
        closest_idx = time_diffs.argmin()
        closest_time = df.index[closest_idx]
        min_time_diff = time_diffs[closest_idx]  # 直接使用索引访问TimedeltaIndex

        # 检查时间差是否在合理范围内（5分钟内）
        if min_time_diff > timedelta(minutes=5):
            return None

        # 处理可能的重复时间戳，取最后一条记录
        previous_prediction = df.loc[closest_time]
        if isinstance(previous_prediction, pd.DataFrame):
            # 如果返回多行，取最后一行
            previous_prediction = previous_prediction.iloc[-1]

        previous_price = float(previous_prediction['close_price'])
        predicted_direction = int(previous_prediction['prediction'])

        # 计算实际价格变化
        price_change = (current_price - previous_price) / previous_price

        # 【关键修复】使用与训练时一致的阈值计算方法
        try:
            # 获取足够的历史数据用于阈值计算
            historical_data = fetch_latest_klines_from_binance(limit=min(1000, 2000))
            if historical_data is not None and len(historical_data) > 100:
                threshold, window, min_periods = calculate_training_consistent_threshold(historical_data)
                print(f"   使用训练一致的动态阈值: ±{threshold:.6f} (窗口={window}, 最小期数={min_periods})")
            else:
                threshold = 0.005
                print(f"   历史数据不足，使用固定阈值: ±{threshold:.6f}")
        except:
            threshold = 0.005
            print(f"   获取历史数据失败，使用固定阈值: ±{threshold:.6f}")

        # 判断实际方向
        actual_direction = int(1 if price_change > threshold else 0)

        # 判断预测是否正确
        is_correct = bool(predicted_direction == actual_direction)

        verification_result = {
            'prediction_time': closest_time,
            'verification_time': current_timestamp,
            'previous_price': previous_price,
            'current_price': current_price,
            'price_change': price_change,
            'predicted_direction': predicted_direction,
            'actual_direction': actual_direction,
            'is_correct': is_correct,
            'threshold_used': threshold
        }

        return verification_result

    except Exception as e:
        print(f"❌ 验证上次预测失败: {e}")
        return None

def update_prediction_accuracy(verification_result):
    """更新预测日志中的准确性信息"""
    try:
        if verification_result is None:
            return

        # 读取预测日志
        df = pd.read_csv(PREDICTION_LOG_PATH)
        # 修复：明确指定datetime格式，避免解析错误
        df['timestamp_utc'] = pd.to_datetime(df['timestamp_utc'], format='%Y-%m-%d %H:%M:%S', errors='coerce')
        # 删除无法解析的时间戳行
        df = df.dropna(subset=['timestamp_utc'])
        if len(df) == 0:
            print("❌ 预测日志中没有有效的时间戳")
            return
        df.set_index('timestamp_utc', inplace=True)

        # 添加验证结果列（如果不存在）
        if 'actual_direction' not in df.columns:
            df['actual_direction'] = np.nan
        if 'is_correct' not in df.columns:
            df['is_correct'] = np.nan
        if 'price_change' not in df.columns:
            df['price_change'] = np.nan

        # 更新对应记录的验证结果
        prediction_time = verification_result['prediction_time']
        if any(df.index == prediction_time):
            df.loc[prediction_time, 'actual_direction'] = int(verification_result['actual_direction'])
            df.loc[prediction_time, 'is_correct'] = bool(verification_result['is_correct'])
            df.loc[prediction_time, 'price_change'] = float(verification_result['price_change'])

        # 保存更新后的数据
        df.to_csv(PREDICTION_LOG_PATH)

    except Exception as e:
        print(f"❌ 更新预测准确性失败: {e}")

def calculate_current_accuracy():
    """计算当前的预测准确率"""
    try:
        if not os.path.exists(PREDICTION_LOG_PATH):
            return None

        df = pd.read_csv(PREDICTION_LOG_PATH)
        if len(df) == 0:
            return None

        # 只统计已验证的预测
        verified_predictions = df.dropna(subset=['is_correct'])

        if len(verified_predictions) == 0:
            return None

        total_verified = len(verified_predictions)
        correct_predictions = verified_predictions['is_correct'].sum()
        overall_accuracy = correct_predictions / total_verified

        # 分类统计
        up_predictions = verified_predictions[verified_predictions['prediction'] == 1]
        down_predictions = verified_predictions[verified_predictions['prediction'] == 0]

        up_accuracy = 0
        down_accuracy = 0

        if len(up_predictions) > 0:
            up_correct = up_predictions['is_correct'].sum()
            up_accuracy = up_correct / len(up_predictions)

        if len(down_predictions) > 0:
            down_correct = down_predictions['is_correct'].sum()
            down_accuracy = down_correct / len(down_predictions)

        return {
            'total_predictions': len(df),
            'verified_predictions': total_verified,
            'correct_predictions': int(correct_predictions),
            'overall_accuracy': overall_accuracy,
            'up_predictions_count': len(up_predictions),
            'down_predictions_count': len(down_predictions),
            'up_accuracy': up_accuracy,
            'down_accuracy': down_accuracy
        }

    except Exception as e:
        print(f"❌ 计算准确率失败: {e}")
        return None

def save_prediction_log(prediction_result):
    """保存预测结果到日志文件"""
    try:
        # 准备日志数据
        # 确保时间戳格式一致
        timestamp_str = prediction_result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
        log_data = {
            'timestamp_utc': timestamp_str,
            'close_price': float(prediction_result['close_price']),
            'prediction': int(prediction_result['prediction']),
            'prediction_proba_down': float(prediction_result['prediction_proba_down']),
            'prediction_proba_up': float(prediction_result['prediction_proba_up']),
            'prediction_confidence': float(prediction_result['prediction_confidence']),
            'actual_direction': np.nan,  # 待验证
            'is_correct': np.nan,        # 待验证
            'price_change': np.nan       # 待验证
        }

        # 转换为DataFrame
        log_df = pd.DataFrame([log_data])
        log_df.set_index('timestamp_utc', inplace=True)

        # 保存到文件
        if os.path.exists(PREDICTION_LOG_PATH):
            log_df.to_csv(PREDICTION_LOG_PATH, mode='a', header=False)
        else:
            log_df.to_csv(PREDICTION_LOG_PATH)

        print(f"📝 预测结果已保存")

    except Exception as e:
        print(f"❌ 保存预测日志失败: {e}")

def run_single_prediction():
    """执行一次预测"""
    print(f"\n🔄 开始预测 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 1. 获取最新市场数据
    klines_data = fetch_latest_klines_from_binance()
    if klines_data is None:
        print("❌ 无法获取市场数据")
        return False

    print(f"📊 获取到 {len(klines_data)} 条K线数据")

    # 2. 验证上一次预测的准确性
    current_price = klines_data['close'].iloc[-1]
    current_timestamp = klines_data.index[-1]

    verification_result = verify_previous_prediction(current_price, current_timestamp)
    if verification_result:
        print(f"🔍 验证上次预测:")
        print(f"   预测时间: {verification_result['prediction_time'].strftime('%m-%d %H:%M')}")
        print(f"   预测方向: {'🔺 上涨' if verification_result['predicted_direction'] == 1 else '🔻 下跌'}")
        print(f"   实际方向: {'🔺 上涨' if verification_result['actual_direction'] == 1 else '🔻 下跌'}")
        print(f"   价格变化: {verification_result['price_change']:+.3%}")
        print(f"   预测结果: {'✅ 正确' if verification_result['is_correct'] else '❌ 错误'}")

        # 更新预测日志中的验证结果
        update_prediction_accuracy(verification_result)

    # 3. 进行新的预测
    prediction_result = make_prediction(klines_data)
    if prediction_result is None:
        print("❌ 预测失败")
        return False

    # 4. 显示预测结果
    print(f"📈 当前预测:")
    print(f"   时间: {prediction_result['timestamp'].strftime('%m-%d %H:%M')}")
    print(f"   当前价格: ${prediction_result['close_price']:.2f}")
    print(f"   预测方向: {'🔺 上涨' if prediction_result['prediction'] == 1 else '🔻 下跌'}")
    print(f"   置信度: {prediction_result['prediction_confidence']:.2%}")

    # 5. 保存预测日志
    save_prediction_log(prediction_result)

    # 6. 显示当前准确率统计
    accuracy_stats = calculate_current_accuracy()
    if accuracy_stats:
        print(f"📊 准确率统计:")
        print(f"   总预测: {accuracy_stats['total_predictions']} | 已验证: {accuracy_stats['verified_predictions']} | 正确: {accuracy_stats['correct_predictions']}")
        print(f"   整体准确率: {accuracy_stats['overall_accuracy']:.1%}")

        if accuracy_stats['up_predictions_count'] > 0:
            print(f"   上涨预测准确率: {accuracy_stats['up_accuracy']:.1%} ({accuracy_stats['up_predictions_count']}次)")
        if accuracy_stats['down_predictions_count'] > 0:
            print(f"   下跌预测准确率: {accuracy_stats['down_accuracy']:.1%} ({accuracy_stats['down_predictions_count']}次)")

    print(f"✅ 预测完成")
    return True

def main():
    """主函数"""
    global running

    print_banner()

    # 加载模型
    if not load_model_and_preprocessors():
        print("❌ 无法加载模型，程序退出")
        return

    prediction_count = 0

    try:
        while running:
            prediction_count += 1
            print(f"\n{'='*20} 第 {prediction_count} 次预测 {'='*20}")

            # 执行预测
            success = run_single_prediction()

            if success:
                print(f"📊 累计预测次数: {prediction_count}")

                if running:  # 检查是否还在运行
                    # 计算下次预测时间
                    next_prediction_time = datetime.now() + timedelta(seconds=PREDICTION_INTERVAL)
                    print(f"⏰ 下次预测时间: {next_prediction_time.strftime('%H:%M:%S')}")

                    # 等待30分钟，每分钟检查一次是否需要停止
                    for i in range(PREDICTION_INTERVAL // 60):  # 30次，每次1分钟
                        if not running:
                            break
                        remaining_minutes = 30 - i
                        print(f"⏳ 等待中... 还有 {remaining_minutes} 分钟", end='\r')
                        time.sleep(60)  # 等待1分钟

                    if running:
                        print(" " * 50, end='\r')  # 清除等待信息
            else:
                print("❌ 预测失败，5分钟后重试...")
                for i in range(5):
                    if not running:
                        break
                    print(f"⏳ 重试倒计时: {5-i} 分钟", end='\r')
                    time.sleep(60)

                if running:
                    print(" " * 30, end='\r')  # 清除倒计时信息

    except KeyboardInterrupt:
        print(f"\n\n🛑 用户手动停止")
    except Exception as e:
        print(f"\n\n❌ 程序异常: {e}")
    finally:
        running = False
        print(f"\n📊 运行总结:")
        print(f"   总预测次数: {prediction_count}")

        # 显示最终统计
        accuracy_stats = calculate_current_accuracy()
        if accuracy_stats:
            print(f"   已验证预测: {accuracy_stats['verified_predictions']}")
            print(f"   整体准确率: {accuracy_stats['overall_accuracy']:.2%}")

        print(f"✅ 程序已安全退出")

if __name__ == "__main__":
    main()
